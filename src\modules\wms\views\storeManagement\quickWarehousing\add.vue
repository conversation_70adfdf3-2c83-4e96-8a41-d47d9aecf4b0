<template>
  <div class="app-container">
    <div class="page-title">
      <el-page-header @back="handleBack">
        <template #content>
          <span class="cursor-pointer mr8px">{{ pageTitle }}</span>
        </template>
      </el-page-header>
    </div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="page-content">
      <!-- 基本信息 -->
      <div class="title-label">
        <div class="title-line"></div>
        <div class="title-content">{{ t('quickWarehousing.label.basicInformation') }}</div>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.receiptNoticeCode')" prop="receiptNoticeCode">
            <el-input v-model="formData.receiptNoticeCode" placeholder="系统生成" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.receiptType')" prop="receiptType">
            <el-select v-model="formData.receiptType" placeholder="请选择" class="w-full" disabled>
              <el-option label="采购入库" :value="1" />
              <el-option label="退货入库" :value="2" />
              <el-option label="直接入库" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.themeDesc')" prop="themeDesc">
            <el-input v-model="formData.themeDesc" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.businessPerson')" prop="salesmanId">
            <el-select v-model="formData.salesmanId" placeholder="请选择" class="w-full" @change="handleSalesmanChange">
              <el-option v-for="item in businessPersonList" :key="item.userId" :label="item.nickName"
                :value="item.userId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.sourceOrderCode')" prop="sourceOrderCode">
            <el-input v-model="formData.sourceOrderCode" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.plannedDeliveryTime')" prop="plannedDeliveryTime">
            <el-date-picker v-model="formData.plannedDeliveryTime" type="datetime" placeholder="计划交货时间,与出库时间一致,默认为提交时间"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" class="w-full-custom" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.supplier')" prop="supplierCode">
            <el-select v-model="formData.supplierCode" filterable allow-create default-first-option placeholder="请选择" class="w-full"
             @change="handleSupplierChange">
              <el-option v-for="item in supplierList" :key="item.supplierCode" :label="item.supplierName"
                :value="item.supplierCode" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="t('quickWarehousing.label.address')">
            <SelAreaCascader :defaultCountryInfo="formData.countryId || ''" :defaultAreaInfo="getSupplierAreaInfo()"
              :defaultDesAddressInfo="formData.address || ''" :isEditMode="Boolean(isEdit)"
              @getCountryInfo="handleSupplierCountryChange" @getAreaInfo="handleSupplierAreaChange"
              @getDesAddressInfo="handleSupplierAddressChange" :flex="true" :paddingTop="'0px'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.contactPerson')" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.mobile')" prop="mobile">
            <el-input v-model="formData.mobile" placeholder="请输入">
              <template #prepend>
                <el-select class="custom-select" v-model="formData.countryAreaCode" style="width: 80px;background: white;">
						<el-option v-for="item in countryNumCodeList" :key="item.id" :label="item.internationalCode"
							:value="item.internationalCode" />
					</el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.weighbridgeNo')" prop="weighbridgeNo">
            <el-input v-model="formData.weighbridgeNo" placeholder="请输入">
              <template #append>
                <el-badge :value="formData.weighbridgeNoAttachment.length" :offset="[10, 5]" class="item"
                  type="primary">
                  <el-button type="primary" link @click="handleAttachmentUpload(formData)">
                    {{ $t('common.uploadBtn') }}
                  </el-button>
                </el-badge>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="t('quickWarehousing.label.vehicleNo')" prop="vehicleNo">
            <el-input v-model="formData.vehicleNo" placeholder="请输入"/>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item :label="t('quickWarehousing.label.entryOrderRemark')" prop="entryOrderRemark">
            <el-input v-model="formData.entryOrderRemark" type="textarea" :rows="1" placeholder="请输入"/>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 商品明细 -->
      <section class="flex align-center justify-between">
        <div class="title-label">
          <div class="title-line"></div>
          <div class="title-content">{{ t('quickWarehousing.label.productDetails') }}</div>
        </div>
        <el-button type="primary" @click="handleAddProduct">
          {{ t('quickWarehousing.button.add') }}
        </el-button>
      </section>
      <!-- 转换量:用户填写入库量后根据转换关系自动计算，可修改 -->
      <!-- 入库金额:用户填写单价/入库量/入库转换量后自动计算，用户可修改，计算公式:金额(2位小数，四舍五入)=单价(4位小数)*入库量或入库转换量 -->
      <!-- 入库单价:同一商品多个库区时入库单价相同，填写一次，其他默认 -->
      <!-- 入库库区:可选择已启用库区 -->
      <!-- 必填项:入库单价/入库量/入库库区/入库金额/入库转换量 -->
      <el-table :data="formData.productList" border style="width: 100%;" class="mb-4" show-summary
        :span-method="handleSpanMethod" :summary-method="handleSummaryMethod">
        <el-table-column type="index" :label="t('quickWarehousing.label.serialNumber')" width="60" align="center" fixed="left" />
        
        <el-table-column :label="t('quickWarehousing.label.productInfo')" min-width="251" fixed="left">
          <template #default="scope">
            <div>
              <span style="color: #90979E;">{{ scope.row.productCode }}</span> <span>{{ scope.row.productName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.unitPrice')" min-width="124" align="right">
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.unitPrice'" :rules="[
              {
                required: true,
                message: '单价不能为空',
                trigger: ['blur', 'change'],
              },
              {
                pattern: /^(-?[0-9][0-9]{0,6}(?:\.\d{1,4})?)$/,
                message: '整数位限长7位，小数后4位',
                trigger: ['blur', 'change'],
              },
            ]">
              <el-input v-model="scope.row.unitPrice" :precision="4" :min="0" controls-position="right"
                @change="calculateAmountByUnitPrice(scope.row)" :disabled="!(scope.row.parentId === '0' || scope.row.parentId === undefined)">
                <template #append>
                  <div class="text-gray-500 text-xs">{{ scope.row.pricingScheme === 0 ? '元/'+scope.row.productUnitName : '元/'+scope.row.conversionRelSecondUnitName }}</div>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 单据号 -->
        <el-table-column :label="t('quickWarehousing.label.billNo')" width="100">
          <template #default="scope">
              <el-input
                v-model="scope.row.billNo"
                placeholder="请输入"
                maxlength="30"
                @input="handleBillNoChange(scope.row, scope.$index)"/>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.actualInQty')" min-width="114" align="right">
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.actualInQty'"
              :rules="[
                {
                  required: true,
                  message: '入库量不能为空',
                  trigger: ['blur', 'change'],
                },
                {
                  pattern: /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                  message: '整数位限长8位，小数后3位',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-input v-model="scope.row.actualInQty" :precision="3" :min="0" controls-position="right"
                @change="convertProductUnitStragery(scope.row, 'FIRST_TO_SECOND', 'actualInQty', 'actualInWeight')">
                <template #append>
                  <div class="text-gray-500 text-xs">{{ scope.row.productUnitName }}</div>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        
        <el-table-column :label="t('quickWarehousing.label.amount')" min-width="124" align="right">
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.amount'" :rules="[
              {
                required: true,
                message: '金额不能为空',
                trigger: ['blur', 'change'],
              },
              {
                pattern:
                  /^-?(0(?:\.\d{1,2})?|[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                message: '整数位限长9位，小数后2位',
                trigger: ['blur', 'change'],
              },
            ]">
              <el-input v-model="scope.row.amount" controls-position="right" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.warehouseArea')" min-width="114">
          <template #header>
            <div class="flex items-center">
              <span>{{ t('quickWarehousing.label.warehouseArea') }}</span>
              <el-dropdown @command="handleBatchSetWarehouseArea" style="margin-left: 8px;">
                <el-icon class="cursor-pointer text-blue-500 hover:text-blue-700">
                  <ArrowDown />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="item in warehouseAreaList"
                      :key="item.areaCode"
                      :command="item.areaCode">
                      {{ item.warehouseName }}|{{ item.areaName }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.warehouseAreaCode'" :rules="[
              {
                required: true,
                message: '入库库区不能为空',
                trigger: ['blur', 'change'],
              },
            ]">
              <el-select v-model="scope.row.warehouseAreaCode" placeholder="请选择" class="w-full">
                <el-option v-for="item in warehouseAreaList" :key="item.areaCode"
                  :label="item.warehouseName + '|' + item.areaName" :value="item.areaCode" />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- 产地 -->
        <el-table-column :label="t('quickWarehousing.label.originPlace')" min-width="114">
          <template #header>
            <div class="flex items-center">
              <span>{{ t('quickWarehousing.label.originPlace') }}</span>
              <el-dropdown @command="handleBatchSetOriginPlace" style="margin-left: 8px;">
                <el-icon class="cursor-pointer text-blue-500 hover:text-blue-700">
                  <ArrowDown />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="item in originPlaceList"
                      :key="item.id"
                      :command="item.id">
                      {{ item.name }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.originPlaceId'" :rules="[
              {
                required: true,
                message: '产地不能为空',
                trigger: ['blur', 'change'],
              },
            ]">
              <el-select v-model="scope.row.originPlaceId" placeholder="请选择" class="w-full">
                <el-option v-for="item in originPlaceList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <!--运费-->
        <el-table-column :label="t('quickWarehousing.label.freight')" min-width="91">
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.shippingAmount'" :rules="[
              {
                pattern:
                  /^-?(0(?:\.\d{1,2})?|[1-9]\d{0,8}(?:\.\d{1,2})?)$/,
                message: '整数位限长9位，小数后2位',
                trigger: ['blur', 'change'],
              }
            ]
            ">
              <el-input v-model="scope.row.shippingAmount" controls-position="right" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.productPackaging')" min-width="95">
          <template #default="scope">
            <el-input v-model="scope.row.productPackaging" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.actualInWeight')" min-width="95" align="right">
          <template #default="scope">
            <el-form-item class="mt15px" label-width="0px" :prop="'productList.' + scope.$index + '.actualInWeight'"
              :rules="[
                {
                  required: true,
                  message: '入库转换量不能为空',
                  trigger: ['blur', 'change'],
                },
                {
                  pattern: /^(0(?:\.\d{1,3})?|[1-9]\d{0,7}(?:\.\d{1,3})?)$/,
                  message: '整数位限长8位，小数后3位',
                  trigger: ['blur', 'change'],
                },
              ]">
              <el-input v-model="scope.row.actualInWeight"
                @change="convertProductUnitStragery(scope.row, 'SECOND_TO_FIRST', 'actualInQty', 'actualInWeight')" :precision="3" :min="0"
                controls-position="right">
                <template #append>
                  <div class="text-gray-500 text-xs">{{ scope.row.conversionRelSecondUnitName }}</div>
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <!--备注-->
        <el-table-column v-if="isEdit && !isDirectStorage" :label="t('quickWarehousing.label.productRemark')" prop="remark">
        </el-table-column>
         <el-table-column :label="t('quickWarehousing.label.productSpecs')" width="100">
          <template #default="scope">
            <span>{{ scope.row.productSpecs }}</span>
          </template>
        </el-table-column>
            <el-table-column :label="t('quickWarehousing.label.productCategory')" width="120">
          <template #default="scope">
            <span>{{ scope.row.fullCategoryName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.qualityCheck')" width="50" align="center" fixed="right">
          <template #default="scope">
            <el-button v-if="!scope.row.parentId || scope.row.parentId === '0'" type="primary" link
              @click="handleQualityCheck(formData.productList, scope.row, scope.$index)">
              质检
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="t('quickWarehousing.label.operation')" width="70" align="center" fixed="right">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="handleAddRow(scope.$index, scope.row)">
              <el-icon>
                <Plus />
              </el-icon>
            </el-button>
            <el-button v-if="formData.productList && formData.productList.length > 0" type="danger" link size="small"
              @click="handleDeleteRow(scope.$index)">
              <el-icon>
                <Minus />
              </el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <!-- 底部按钮 -->
    <div class="fixed-footer">
      <el-button @click="handleBack">{{ t('quickWarehousing.button.cancel') }}</el-button>
      <el-button @click="handleSaveDraft" :loading="saving">{{ t('quickWarehousing.button.saveDraft') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ t('quickWarehousing.button.submit') }}
      </el-button>
    </div>

    <!-- 商品选择弹窗 -->
    <AddProduct ref="addGoodsRef" v-model:visible="productDialog.visible" :title="productDialog.title"
      @onSubmit="onProductSubmit" />

    <!-- 质检商品弹窗 -->
    <QualityCheckDialog v-model:visible="qualityDialog.visible" :product-data="qualityDialog.productData"
      @confirm="onQualityConfirm" :type="'update'"/>

    <!-- 上传弹窗 -->
    <UploadDialog ref="uploadDialogRef" v-model:visible="uploadDialog.visible" @onSubmit="onSubmitUpload" />

    <!-- 供应商输入弹窗 -->
    <el-dialog v-model="supplierDialog.visible" title="添加供应商" width="400px" :before-close="handleSupplierDialogClose">
      <el-form ref="supplierFormRef" :model="supplierDialog.form" :rules="supplierDialog.rules" label-width="100px">
        <el-form-item label="供应商名称" prop="supplierName">
          <el-input v-model="supplierDialog.form.supplierName" placeholder="请输入供应商名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleSupplierDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSupplierDialogConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Plus, Minus, Edit, ArrowDown } from "@element-plus/icons-vue";
import QuickWarehousingAPI, { QuickWarehousingFormData } from "@/modules/wms/api/quickWarehousing";
import AddProduct from "./components/addProduct.vue";
import QualityCheckDialog from "./components/QualityCheckDialog.vue";
import SelAreaCascader from "@/modules/wms/components/SelAreaCascader.vue";
import CommonAPI from "@/modules/wms/api/common";
import UploadDialog from "./components/uploadDialog.vue";
// import { useQuickWarehousing } from "./composables";
import { parseTime } from "@/core/utils";
import UserAPI from "@/core/api/accountManagement";
// const { convertProductUnit, convertProductUnitStragery, calculateAmount } = useQuickWarehousing();
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { useConvertProductUnit } from "@/modules/wms/composables";
const { convertProductUnitStragery, calculateAmount } = useConvertProductUnit();
import { useTagsViewStore } from "@/core/store/modules/tagsView";
const tagsViewStore = useTagsViewStore();
/* defineOptions({
  name: "AddQuickWarehousing",
  inheritAttrs: false,
}); */
const addGoodsRef = ref();
const route = useRoute();
const router = useRouter();

const pageTitle = computed(() => {
  return route.query.type === "edit" ? "编辑快速入库" : "新增快速入库";
});

const isEdit = computed<boolean>(() => {
  return route.query.type === "edit";
});

// 直接入库
const isDirectStorage = computed<boolean>(() => {
  return route.query.receiptType === "4";
})

const receiptType = route.query.receiptType

const formRef = ref();
const saving = ref(false);
const submitting = ref(false);
const countryAreaCode = ref('+86');
const countryNumCodeList = ref([]);
const originPlaceList = ref([]);

function getOriginPlaceList() {
	QuickWarehousingAPI.queryOriginPlaceList()
		.then((data) => {
			originPlaceList.value = data;
		})
		.finally(() => { });
}


// 表单数据
const formData = reactive<QuickWarehousingFormData>({
  // 基本信息
  receiptNoticeCode: "", // 入库通知单号
  receiptType: 4, // 入库类型:1:采购入库、2:退货入库、3:调拨入库、4:直接入库、5:地头入库
  themeDesc: "", // 主题描述
  sourceOrderCode: "", // 来源单号
  // purchaseSalesPerson: "", // 采购/销售员
  plannedDeliveryTime: "", // 计划交货时间
  // status: 0, // 状态:0:草稿、1:初始 2：完结 3:取消

  // 供应商信息
  supplierCode: "", // 供应商编码
  supplierName: "", // 供应商名称

  /*  
  // 客户信息
  customerCode: "", // 客户编码
  customerName: "", // 客户名称 
  */

  // 地址信息
  countryId: "", // 国家id
  countryName: "", // 国家名称
  countryAreaCode: "+86", // 国家区域代码-手机区号
  provinceId: "", // 省份id
  provinceName: "", // 省名称
  cityId: "", // 城市id
  cityName: "", // 市名称
  districtId: "", // 区县id
  districtName: "", // 区县名称
  address: "", // 详细地址

  // 联系信息
  contactPerson: "", // 联系人
  mobile: "", // 手机号

  // 合同信息
  contractCode: "", // 合同编码
  contractName: "", // 合同名称
  contractType: undefined, // 合同类型

  // 业务信息
  salesmanId: "", // 业务员ID
  salesmanName: "", // 业务员姓名
  weighbridgeNo: "", // 榜单编号
  weighbridgeNoAttachment: "", // 榜单编号附件
  vehicleNo: "", // 入库车号
  entryOrderRemark: "", // 备注
   /* // 仓库信息
   warehouseCode: "", // 仓库编码
   warehouseId: undefined, // 仓库id
   warehouseName: "", // 仓库名称 

  // 金额信息
  currency: "CNY", // 交易币种：CNY->人民币，USD->美元
  totalAmount: 0, // 总入库金额
  totalCostAmount: 0, // 总成本金额
  expectedQty: 0, // 计划总数
  expectedWeight: 0, // 计划总重量
  actualTotalQuantity: 0, // 总入库量（实际总量）
  actualTotalWeight: 0, // 总入库转换量（实际总数量）

  // 其他信息
  source: 1, // 来源：1:手动创建 2:同步
  sourceSystem: "", // 来源系统
  productQty: 0, // 商品个数 */

  // 商品列表
  productList: [
    /*  {
       id: undefined,
       productCode: "", // 商品编码
       productName: "", // 商品名称
       productSpecs: "", // 规格
       productPackaging: "", // 商品包装
       
       // 分类信息
       firstCategoryId: undefined, // 一级分类id
       firstCategoryName: "", // 一级分类名称
       secondCategoryId: undefined, // 二级分类id
       secondCategoryName: "", // 二级分类名称
       thirdCategoryId: undefined, // 三级分类id
       thirdCategoryName: "", // 三级分类名称
       
       // 单位信息
       productUnitId: undefined, // 商品采购单位id
       productUnitName: "", // 商品采购单位名称
       conversionRelSecondUnitId: undefined, // 商品换算关系第二个值的单位id
       conversionRelSecondUnitName: "", // 商品换算关系第二个值的单位名称
       
       // 数量信息
       productExpectQty: 0, // 商品数量(期望)
       actualInQty: 0, // 本次实际入库数量
       inWarehouseQty: 0, // 已入库数量
       
       // 重量信息
       expectedWeight: 0, // 计划重量
       actualInWeight: 0, // 本次实际入库重量
       inWarehouseWeight: 0, // 已入库重量
       receivedWeight: 0, // 实收总重量
       weight: 0, // 重量（KG）
       
       // 价格信息
       unitPrice: 0, // 入库单价
       amount: 0, // 入库金额
       costUnitPrice: 0, // 成本单价
       costAmount: 0, // 成本金额
       currency: "CNY", // 交易币种
       
       // 仓库信息
       warehouseCode: "", // 仓库编码
       warehouseName: "", // 仓库名称
       warehouseAreaCode: "", // 仓库库区编码
       warehouseAreaName: "", // 仓库库区名称
       
       // 其他信息
       receiptNoticeCode: "", // 入库通知单号
       receiptNoticeId: undefined, // 入库通知单id
       parentId: undefined, // 父id
       isSku: 0, // 是否sku->1:是;0:否
       isDiscreteUnit: 0, // 一级单位增减->1:开启；0:关闭
       pricingScheme: 0, // 计价模式->0:一级单位;1:二级单位
       remark: "", // 备注
     }, */
  ],

  // 质检列表
  qualityInspectionList: [
    /*  {
       productCode: "", // 商品编码
       deductionAmount: 0, // 扣款金额
       deductionDesc: "", // 扣款说明
       deductionAttachment: "", // 附件
       detailList: [
         {
           specification: "", // 规格
           quantity: 0, // 数量
           proportion: 0, // 占比
         }
       ],
     } */
  ],
});

// 表单验证规则
const rules = {
  receiptType: [{ required: true, message: "请选择入库类型", trigger: "change" }],
  plannedDeliveryTime: [
    { required: true, message: "请选择计划交货时间", trigger: "change" },
  ],
  // supplierCode: [{ required: true, message: "请选择供应商", trigger: "change" }],
};


// 基础数据
const businessPersonList = ref<any[]>([]);
const supplierList = ref<any[]>([]);
const warehouseAreaList = ref<any[]>([]);

// 弹窗控制
const productDialog = reactive({
  visible: false,
  title: "选择商品",
});

const qualityDialog = reactive({
  visible: false,
  productData: {},
});

// 方法
const handleBack = async () => {
  await tagsViewStore.delView(route);
  await router.push({
    path: '/wms/quickWarehousing'
  })
};

const handleSummaryMethod = (param: any) => {
  const { columns, data } = param;
  const sums: string[] = [];
  
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计';
    } else if (index === 5 || index === 6 || index === 7) {
      // 计算第5、6、7列的和
      const values = data.map((item: any) => {
        if (index === 5) {
          return Number(item.actualInQty) || 0; // 入库量
        } else if (index === 6) {
          return Number(item.actualInWeight) || 0; // 入库转换量
        } else if (index === 7) {
          return Number(item.amount) || 0; // 入库金额
        }
        return 0;
      });
      
      if (values.every((value: number) => value === 0)) {
        sums[index] = '';
      } else {
        const sum = values.reduce((prev: number, curr: number) => {
          return prev + curr;
        }, 0);
        
        // 根据列类型格式化显示
        if (index === 7) {
          // 金额保留2位小数
          sums[index] = sum.toFixed(2);
        } else {
          // 数量保留3位小数
          sums[index] = sum.toFixed(3);
        }
      }
    } else {
      sums[index] = ' ';
    }
  });
  
  return sums;
}
// 表格行合并方法
const handleSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
  // 只对商品信息、商品分类、规格这三列进行合并
  if (columnIndex === 1 || columnIndex === 2) {
    const currentProductCode = row.productCode;

    if (!currentProductCode) {
      return [1, 1];
    }

    // 计算连续相同productCode的行数
    let rowspan = 1;
    let startIndex = rowIndex;

    // 向前查找连续相同的productCode
    for (let i = rowIndex - 1; i >= 0; i--) {
      if (formData.productList[i].productCode === currentProductCode) {
        startIndex = i;
        rowspan++;
      } else {
        break;
      }
    }

    // 向后查找连续相同的productCode
    for (let i = rowIndex + 1; i < formData.productList.length; i++) {
      if (formData.productList[i].productCode === currentProductCode) {
        rowspan++;
      } else {
        break;
      }
    }

    // 如果当前行是第一个相同productCode的行，返回合并的行数
    if (rowIndex === startIndex) {
      return [rowspan, 1];
    } else {
      // 如果不是第一行，则隐藏该单元格
      return [0, 0];
    }
  }

  return [1, 1];
};

// 上传弹窗状态
const uploadDialog = reactive({
  visible: false,
});

// 供应商弹窗状态
const supplierDialog = reactive({
  visible: false,
  form: {
    supplierName: ''
  },
  rules: {
    supplierName: [
      { required: true, message: '请输入供应商名称', trigger: 'blur' },
      { min: 2, max: 50, message: '供应商名称长度在 2 到 50 个字符', trigger: 'blur' }
    ]
  }
});

// const addProductRef = ref();
const uploadDialogRef = ref();
// const currentUploadIndex = ref<number>();

const handleAttachmentUpload = (formData: any) => {
  uploadDialog.visible = true;
  uploadDialogRef.value?.setEditType("add");
  if (formData.weighbridgeNoAttachment) {
    // 新增快速入库单，文件返回的是数组
    if(Array.isArray(formData.weighbridgeNoAttachment)){
      uploadDialogRef.value?.setFormData(formData.weighbridgeNoAttachment);
    }else{
      // 编辑快速入库单，文件返回的是序列后的对象数组
      uploadDialogRef.value?.setFormData(JSON.parse(formData.weighbridgeNoAttachment));
    }
  }
}

const onSubmitUpload = (data: any) => {
  formData.weighbridgeNoAttachment = data;
}

const handleAddProduct = () => {
  productDialog.visible = true;
  nextTick(() => {
    addGoodsRef.value.queryGoodList();
    addGoodsRef.value.queryManagerCategoryList();
  })
};

// 添加新商品
const onProductSubmit = async (products: any[]) => {
  console.log(products);
  products.forEach(async (product: any) => {
    const existingIndex = formData.productList?.findIndex(
      (item: any) => item.productCode === product.productCode
    );

    if (existingIndex !== undefined && existingIndex >= 0) {
      // 如果商品已存在，更新数量
      if (formData.productList && formData.productList[existingIndex]) {
        formData.productList[existingIndex].actualInQty += product.actualInQty || 1;
      }
    } else {
      // 如果是新商品，添加到列表
      if (formData.productList) {
        // 查询商品最新入库单价
        const unitPrice = await QuickWarehousingAPI.queryProductLatestUnitPrice({
          productCode: product.productCode,
        });
        formData.productList.push({
          // 商品信息
          id: product.id,
          productCode: product.productCode,
          productName: product.productName,

          // productUnitName: product.productUnitName,
          // 商品分类
          firstCategoryId: product.firstCategoryId,
          firstCategoryName: product.firstCategoryName,
          secondCategoryId: product.secondCategoryId,
          secondCategoryName: product.secondCategoryName,
          thirdCategoryId: product.thirdCategoryId,
          thirdCategoryName: product.thirdCategoryName,

          fullCategoryName: product.fullCategoryName,
          // 规格
          productSpecs: product.productSpec,
          // 入库单价
          unitPrice: unitPrice || null,
          // 入库量
          actualInQty: null,
          // 入库转换量
          actualInWeight: null,
          // 入库金额
          amount: null,
          // 入库库区
          warehouseAreaCode: null,
          // 商品包装
          productPackaging: null,
          isSku: product.isSku,
          isDiscreteUnit: product.isDiscreteUnit,
          pricingScheme: product.pricingScheme,
          conversionRelSecondUnitId: product.conversionRelSecondUnitId,
          conversionRelSecondUnitName: product.conversionRelSecondUnitName,

          productUnitId: product.productUnitId,
          productUnitName: product.productUnitName,
          shippingAmount: null, // 运费
          remark: product.remark,
        });
        
      }
    }
  });
};

//  供应商选择
const handleSupplierChange = (value: string) => {
  if(value === 'other'){
    // 打开供应商输入弹窗
    supplierDialog.visible = true;
    supplierDialog.form.supplierName = formData.supplierName;
    return;
  }
  supplierList.value.forEach((item: any) => {
    if (item.supplierCode === value) {
      formData.supplierName = item.supplierName;
    }
  });
}

//  业务员选择
const handleSalesmanChange = (value: string) => {
  businessPersonList.value.forEach((item: any) => {
    if (item.userId === value) {
      formData.salesmanName = item.nickName;
    }
  });
}


const handleAddRow = (index: number, row: any) => {
  if (formData.productList) {
    const insertData = {
      ...row,
      actualInQty: null, // 清空需要重新输入和计算的字段
      actualInWeight: null,
      amount: null,
      warehouseAreaCode: null,
      productPackaging: null,
      parentId: row.id, // 父级id,用户和父级产品id构建层级关系
    }
    formData.productList.splice(index + 1, 0, insertData);
  }
};

const handleDeleteRow = (index: number) => {
  const row = formData.productList?.[index];
  if (formData.productList && formData.productList.length > 0) {
    formData.productList.splice(index, 1);
  }

  // 判断同productCode的商品是否存在
  const productCode = row.productCode;
  const productList = formData.productList?.filter((item: any) => item.productCode === productCode);
  // 解决的问题：质检按钮只在主商品层级显示，如果删除了主商品，质检按钮就会消失，所以需要将parentId设置为0
  if (productList && productList.length > 0) {
    // parentId默认是主商品
    productList[0].parentId = "0";
  }
};

const calculateAmountByUnitPrice = (row: any) => {
  // 找到同productCode商品，重新计算入库金额
  const productCode = row.productCode;
  const productList = formData.productList?.filter((item: any) => item.productCode === productCode);
  if (productList && productList.length > 0) {
    productList.forEach((item: any) => {
      item.unitPrice = row.unitPrice;
      calculateAmount(item, 'actualInQty', 'actualInWeight', 'unitPrice');
    });
  }
}

// 质检弹窗
const handleQualityCheck = (list: any, product: any, index: number) => {
  // 入库量和转换量取同商品productCode对应的字段之和
  const productCode = product.productCode;
  const actualInQty = list.filter((item: any) => item.productCode === productCode)
    .reduce((sum: number, item: any) => sum + (parseFloat(item.actualInQty) || 0), 0);
  const actualInWeight = list.filter((item: any) => item.productCode === productCode)
    .reduce((sum: number, item: any) => sum + (parseFloat(item.actualInWeight) || 0), 0);
  // 从已有的质检信息中找出productCode对应的质检信息
  const existingQuality = formData.qualityInspectionList?.find((item: any) => item.productCode === productCode);
  if (existingQuality) {
    qualityDialog.productData = {
      ...existingQuality,
      ...product,
      actualInQty, 
      actualInWeight,
    }
  }
  else {
    qualityDialog.productData = {
      ...product, index, list, actualInQty, actualInWeight, detailList: [
        {
          specification: "",
          quantity: null,
          proportion: null,
          proportionDisplay: "",
        },
      ],
      deductionAmount: null, // 扣款金额
      deductionDesc: "", // 扣款说明
      deductionAttachment: "", // 扣款附件  
    };
  }

  qualityDialog.visible = true;
};

const onQualityConfirm = (data: any) => {
  // 处理质检结果
  const { index, ...qualityData } = data;
  // 根据productCode判断是否存在，存在则更新，不存在则新增  
  const existingIndex = formData.qualityInspectionList?.findIndex((item: any) => item.productCode === qualityData.productCode);
  if (existingIndex !== undefined && existingIndex >= 0) {
    formData.qualityInspectionList[existingIndex] = qualityData;
  } else {
    formData.qualityInspectionList?.push(qualityData);
  }
};

// 地址选择相关方法
const getSupplierAreaInfo = () => {
  const formDataAny = formData as any;

  if (formDataAny.provinceId && formDataAny.cityId && formDataAny.districtId) {
    return [formDataAny.provinceId, formDataAny.cityId, formDataAny.districtId];
  }
  return [];
};


const handleSupplierCountryChange = (countryInfo: any) => {
  const formDataAny = formData as any;
  if (countryInfo) {
    formDataAny.countryId = countryInfo.id;
    formDataAny.countryName = countryInfo.shortName;
  } else {
    formDataAny.countryId = '';
    formDataAny.countryName = '';
  }
};

const handleSupplierAreaChange = (areaInfo: any) => {
  const formDataAny = formData as any;
  if (areaInfo && areaInfo.pathValues && areaInfo.pathLabels) {
    const values = areaInfo.pathValues;
    const labels = areaInfo.pathLabels;

    formDataAny.provinceId = values[0] || '';
    formDataAny.provinceName = labels[0] || '';
    formDataAny.cityId = values[1] || '';
    formDataAny.cityName = labels[1] || '';
    formDataAny.districtId = values[2] || '';
    formDataAny.districtName = labels[2] || '';
  } else {
    formDataAny.provinceId = '';
    formDataAny.provinceName = '';
    formDataAny.cityId = '';
    formDataAny.cityName = '';
    formDataAny.districtId = '';
    formDataAny.districtName = '';
  }
};

const handleSupplierAddressChange = (address: string) => {
  formData.address = address || '';
};

// 批量设置库区
const handleBatchSetWarehouseArea = (warehouseAreaCode: string) => {
  if (!warehouseAreaCode || !formData.productList) {
    return;
  }

  // 只设置未选择库区的商品
  formData.productList.forEach(product => {
    if (!product.warehouseAreaCode) {
      product.warehouseAreaCode = warehouseAreaCode;
    }
  });

  ElMessage.success(t('quickWarehousing.message.batchSetWarehouseAreaSuccess'));
};

// 批量设置产地
const handleBatchSetOriginPlace = (originPlaceId: string) => {
  if (!originPlaceId || !formData.productList) {
    return;
  }

  // 只设置未选择产地的商品
  formData.productList.forEach(product => {
    if (!product.originPlaceId) {
      product.originPlaceId = originPlaceId;
    }
  });

  ElMessage.success(t('quickWarehousing.message.batchSetOriginPlaceSuccess'));
};

// 地址过滤函数，如果字段为空则不拼接
const filterAddress = (data: any, type?: string) => {
  if (type === 'customer') {
    // 客户地址拼接
    const addressParts = [
      data.customerCountryName,
      data.customerProvinceName,
      data.customerCityName,
      data.customerDistrictName,
      data.customerAddress
    ].filter(part => part && part.trim() !== ''); // 过滤空值和空字符串
    
    return addressParts.join('');
  } else {
    // 供应商地址拼接
    const addressParts = [
      data.countryName,
      data.provinceName,
      data.cityName,
      data.districtName,
      data.address
    ].filter(part => part && part.trim() !== ''); // 过滤空值和空字符串
    
    return addressParts.join('');
  }
};

// 供应商弹窗相关函数
const supplierFormRef = ref();

const handleSupplierDialogClose = () => {
  // 取消时重置供应商选择
  formData.supplierCode = '';
  formData.supplierName = '';
  supplierDialog.visible = false;
  supplierDialog.form.supplierName = '';
};

const handleSupplierDialogConfirm = async () => {
  try {
    await supplierFormRef.value.validate();
    
    // 设置自定义供应商信息
    formData.supplierName = supplierDialog.form.supplierName;
    formData.supplierCode = supplierDialog.form.supplierName;


    supplierList.value.push({
      supplierCode: supplierDialog.form.supplierName,
      supplierName: supplierDialog.form.supplierName,
    });
    
    // 关闭弹窗
    supplierDialog.visible = false;
    supplierDialog.form.supplierName = '';
    
    // ElMessage.success('供应商添加成功');
  } catch (error) {
    console.error('供应商信息验证失败:', error);
  }
};

/*const handleCancel = () => {
  router.back();
};*/

const handleSaveDraft = async () => {
  try {
    formData.plannedDeliveryTime = formData.plannedDeliveryTime ? new Date(formData.plannedDeliveryTime).getTime() : new Date().getTime();

    await formRef.value.validate();
    saving.value = true;
    formData.status = 0; // 草稿状态
    
    const formDataCopy = { ...formData };
    
    formDataCopy.weighbridgeNoAttachment = JSON.stringify(formDataCopy.weighbridgeNoAttachment);
    await QuickWarehousingAPI.addDraft(formDataCopy);
    ElMessage.success("保存成功");
    // router.back();
    await handleBack();
  } catch (error) {
    console.error("保存失败:", error);
  } finally {
    saving.value = false;
  }
};

const handleSubmit = async () => {
  try {
    formData.plannedDeliveryTime = formData.plannedDeliveryTime ? new Date(formData.plannedDeliveryTime).getTime() : new Date().getTime();

    await formRef.value.validate();
    submitting.value = true;
    
    const formDataCopy = { ...formData };
    formDataCopy.weighbridgeNoAttachment = JSON.stringify(formDataCopy.weighbridgeNoAttachment);
    await QuickWarehousingAPI.add(formDataCopy);

    ElMessage.success("提交成功");
    await handleBack();
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    submitting.value = false;
  }
};

// 获取基础数据
const getBaseData = async () => {
  try {
    // 获取仓库库区列表
    const areaData: any = await QuickWarehousingAPI.getWarehouseAreaList({status: 1});
    warehouseAreaList.value = areaData;

    // 获取业务员列表
    const businessPersonData: any = await QuickWarehousingAPI.querySalesPersonUser();
    businessPersonList.value = businessPersonData || [];

    // 获取供应商列表 状态:0-禁用 1-正常
    const supplierData: any = await QuickWarehousingAPI.getSupplierListAll({enableStatus: 1});
    supplierList.value = supplierData || [];
    supplierList.value.push({
      supplierCode: 'other',
      supplierName: '其他',
    });
    // getAreaList();
    getOriginPlaceList();
  } catch (error) {
    console.error("获取基础数据失败:", error);
  }
};

// 获取详情数据
const getDetailData = async () => {
  if (route.query.id) {
    try {
      const data: any = await QuickWarehousingAPI.queryDetail({ id: route.query.id });
      Object.assign(formData, data);
      formData.qualityInspectionList = data.qualityInspectionList || [];
      formData.weighbridgeNoAttachment = JSON.parse(data.weighbridgeNoAttachment);
      formData.plannedDeliveryTime = parseTime(data.plannedDeliveryTime, '{y}-{m}-{d} {h}:{i}:{s}');
    } catch (error) {
      console.error("获取详情失败:", error);
    }
  }
};

onMounted(() => {
  getBaseData();
  getDetailData();
});
</script>

<style scoped lang="scss">
:deep(.page-content .el-form-item){
  margin-bottom: 16px;
}
:deep(.page-content .el-input-group__append){
  padding-left: 8px;
  padding-right: 8px;
}
.align-center {
  align-items: center;
}

.title-label {
  display: flex;
  align-items: center;
  margin: 0px 0 16px 0;

  .title-line {
    width: 4px;
    height: 16px;
    background: var(--el-color-primary);
    margin-right: 8px;
  }

  .title-content {
    font-size: 16px;
    font-weight: 500;
    color: #151719;
  }

  .flex-1 {
    flex: 1;
  }

  .add-product-btn {
    margin-left: auto;
  }
}

.page-title {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7f3;
  font-size: 18px;
  font-weight: 500;
  color: #151719;

  .mr8px {
    margin-right: 8px;
  }
}

.fixed-footer {
  /* position: fixed;
  bottom: 0;
  right: 0;
  left: 0; */
  width: 100%;
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  text-align: right;
  z-index: 100;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-container {
  background: #ffffff;
  border-radius: 4px;
}

.page-content {
  padding: 0px 24px 0 24px;
}

/* 表头批量设置样式 */
.el-table th .cell {
  overflow: visible !important;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.cursor-pointer {
  cursor: pointer;
}

.text-blue-500 {
  color: #3b82f6;
}

.hover\:text-blue-700:hover {
  color: #1d4ed8;
}
</style>
